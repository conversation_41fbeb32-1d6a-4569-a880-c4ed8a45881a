'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

export default function Home() {
  const [dateTime, setDateTime] = useState('Loading time and weather...');

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      const timeStr = now.toLocaleTimeString('vi-VN');
      const dateStr = now.toLocaleDateString('vi-VN', options);

      // Placeholder weather (replace with real API if needed)
      const weather = "Nhiệt độ: 30°C - Trời nắng";

      setDateTime(`${dateStr} - ${timeStr} | ${weather}`);
    };

    updateDateTime();
    const interval = setInterval(updateDateTime, 60000);

    return () => clearInterval(interval);
  }, []);

  const handleWidgetClick = (url: string) => {
    window.location.href = url;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-800 to-gray-600 text-white flex flex-col items-center justify-center p-5 text-center font-sans">
      <header className="flex flex-col items-center mb-8">
        <div className="flex items-center gap-5 mb-4">
          <Image
            src="/logo-company.svg"
            alt="Company Logo"
            width={60}
            height={60}
            className="h-15"
          />
          <Image
            src="/logo-app.svg"
            alt="App Logo"
            width={60}
            height={60}
            className="h-15"
          />
        </div>
        <div className="text-xl mb-1">Chào mừng đến với hệ thống thông tin FTI</div>
        <div className="text-base text-slate-300 mb-8">{dateTime}</div>
      </header>

      <h1 className="text-4xl mb-8">FTI Information System</h1>

      <div className="flex gap-8 flex-wrap justify-center">
        <div
          className="bg-gray-900 border-2 border-blue-500 rounded-2xl p-8 cursor-pointer transition-all duration-200 ease-in-out w-64 shadow-lg hover:-translate-y-1 hover:bg-blue-700"
          onClick={() => handleWidgetClick('https://domain1.example.com')}
        >
          <h2 className="text-2xl mb-3">System Portal</h2>
          <p className="text-base opacity-80">Access the internal system dashboard</p>
        </div>

        <div
          className="bg-gray-900 border-2 border-blue-500 rounded-2xl p-8 cursor-pointer transition-all duration-200 ease-in-out w-64 shadow-lg hover:-translate-y-1 hover:bg-blue-700"
          onClick={() => handleWidgetClick('https://domain2.example.com')}
        >
          <h2 className="text-2xl mb-3">Customer Service</h2>
          <p className="text-base opacity-80">Manage customer profiles and services</p>
        </div>
      </div>

      <footer className="absolute bottom-3 w-full text-center text-sm text-gray-400">
        &copy; 2025 FTI ISC. All rights reserved.
      </footer>
    </div>
  );
}
