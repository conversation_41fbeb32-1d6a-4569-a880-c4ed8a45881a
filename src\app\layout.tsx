import type { Metadata } from "next";
import "./globals.css";

import localFont from 'next/font/local';

const inter = localFont({
  src: [
    {
      path: './fonts/Inter-VariableFont_opsz,wght.ttf',
      style: 'normal',
      weight: '100 900'
    },
    {
      path: './fonts/Inter-Italic-VariableFont_opsz,wght.ttf',
      style: 'italic',
      weight: '100 900'
    }
  ],
  variable: '--font-inter',
  display: 'swap',
});
export const metadata: Metadata = {
  title: "FTI Information System",
  description: "Chào mừng đến với hệ thống thông tin FTI",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={inter.variable}
      >
        {children}
      </body>
    </html>
  );
}
